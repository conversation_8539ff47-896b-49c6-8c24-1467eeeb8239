<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.fragment.ReadWriteTag">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_3dp"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/tag_select"/>

        <Spinner
            android:id="@+id/spinner_epc"
            android:background="@drawable/spinner_background"
            android:minHeight="@dimen/min_height_40dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1" />
        <CheckBox
            android:id="@+id/checkbox_filter"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:checked="true"
            android:text="@string/filter"/>

    </LinearLayout>
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/teal_700"
        android:textSize="@dimen/text_size_18sp"
        android:text="@string/read_write_tag"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_3dp"
        android:orientation="horizontal">
        <RadioGroup
            android:id="@+id/radio_membank"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/radioButton_epc"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="@string/epc" />
            <RadioButton
                android:id="@+id/radioButton_tid"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="@string/tid" />
            <RadioButton
                android:id="@+id/radioButton_user"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="@string/user" />
            <RadioButton
                android:id="@+id/radioButton_password"
                android:layout_width="wrap_content"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:text="@string/password" />

        </RadioGroup>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_3dp"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/start_addr"/>
        <EditText
            android:id="@+id/editText_start_addr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:singleLine="true"
            android:text="0"
            android:minWidth="@dimen/min_height_60dp"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/length"/>
        <EditText
            android:id="@+id/editText_len"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:inputType="number"
            android:singleLine="true"
            android:text="1"
            android:minWidth="@dimen/min_height_60dp"/>

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_3dp"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/access_password"/>
        <EditText
            android:id="@+id/editText_access_password"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:text="00000000"
            android:minWidth="100dp"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_3dp"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/data"/>
        <EditText
            android:id="@+id/editText_write_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:layout_weight="1"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_3dp"
        android:orientation="vertical">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/read_data"/>
        <EditText
            android:id="@+id/editText_read_data"
            android:layout_width="match_parent"
            android:padding="@dimen/margin_3dp"
            android:gravity="top"
            android:layout_height="90dp"
            android:background="@drawable/corners_background"
            />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_3dp"
        android:orientation="horizontal">
        <Button
            android:id="@+id/button_read"
            android:layout_margin="@dimen/margin_3dp"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:minHeight="@dimen/min_height_60dp"
            android:layout_height="wrap_content"
            android:text="@string/read"
            />
        <Button
            android:id="@+id/button_write"
            android:layout_margin="@dimen/margin_3dp"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/min_height_60dp"
            android:text="@string/write"
            />
        <Button
            android:id="@+id/button_clean"
            android:layout_margin="@dimen/margin_3dp"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/min_height_60dp"
            android:text="@string/clean"
            />
    </LinearLayout>

     <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/teal_700"
            android:textSize="@dimen/text_size_18sp"
            android:text="@string/modify_epc"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_3dp"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_3dp"
                    android:orientation="horizontal">
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/new_epc"/>
                    <EditText
                        android:id="@+id/editText_new_epc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:hint="@string/please_input_hex_string"
                        android:singleLine="true"
                        android:layout_weight="1"/>
                     </LinearLayout>

                <Button
                    android:id="@+id/button_modify"
                    android:layout_margin="@dimen/margin_3dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/modify_epc"
                    android:minHeight="@dimen/min_height_60dp"
                    />
            </LinearLayout>
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/teal_700"
        android:textSize="@dimen/text_size_18sp"
        android:text="@string/lock_tag"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/margin_3dp"
            android:orientation="horizontal">
            <TextView
                android:layout_margin="@dimen/margin_3dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/lock_data"
                />
            <Spinner
                android:id="@+id/sipnner_lock_data"
                android:layout_margin="@dimen/margin_3dp"
                android:background="@drawable/spinner_background"
                android:minHeight="@dimen/min_height_40dp"
                android:entries="@array/lock_data"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/margin_3dp"
            android:orientation="horizontal">
            <TextView
                android:layout_margin="@dimen/margin_3dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/lock_type"
                />
            <Spinner
                android:id="@+id/sipnner_lock_type"
                android:layout_margin="@dimen/margin_3dp"
                android:background="@drawable/spinner_background"
                android:minHeight="@dimen/min_height_40dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:entries="@array/lock_type"
                />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/margin_3dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:text="@string/access_password"/>
            <EditText
                android:id="@+id/editText_lock_password"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="100dp"/>
            <Button
                android:id="@+id/button_lock"
                android:layout_marginLeft="@dimen/min_height_60dp"
                android:layout_gravity="right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/min_height_60dp"
                android:text="@string/lock_tag"
                />
        </LinearLayout>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/lock_tips"/>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/teal_700"
            android:textSize="@dimen/text_size_18sp"
            android:text="@string/kill_tag"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/margin_3dp"
            android:layout_marginBottom="@dimen/min_height_40dp"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kill_password"/>
            <EditText
                android:id="@+id/editText_kill_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:minWidth="100dp"/>
            <Button
                android:id="@+id/button_kill"
                android:layout_marginLeft="@dimen/min_height_60dp"
                android:layout_gravity="right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/min_height_60dp"
                android:text="@string/kill_tag"
                />
        </LinearLayout>

    </LinearLayout>
    </ScrollView>

</LinearLayout>