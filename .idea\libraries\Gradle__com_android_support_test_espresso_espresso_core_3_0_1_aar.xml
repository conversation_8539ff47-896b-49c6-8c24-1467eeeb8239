<component name="libraryTable">
  <library name="Gradle: com.android.support.test.espresso:espresso-core:3.0.1@aar">
    <CLASSES>
      <root url="jar://G:/AndroidCache/.gradle/caches/transforms-2/files-2.1/dfccecf417f72327d66fc8807f757b33/espresso-core-3.0.1/jars/classes.jar!/" />
      <root url="file://G:/AndroidCache/.gradle/caches/transforms-2/files-2.1/dfccecf417f72327d66fc8807f757b33/espresso-core-3.0.1/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://G:/AndroidCache/.gradle/caches/modules-2/files-2.1/com.android.support.test.espresso/espresso-core/3.0.1/1b1e7d702208711e2f872d32e52539fb21427d77/espresso-core-3.0.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://G:/AndroidCache/.gradle/caches/modules-2/files-2.1/com.android.support.test.espresso/espresso-core/3.0.1/2745fc2c78d3751db6c92cdc15dc760a08a8b145/espresso-core-3.0.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>