<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item name="nav_host_fragment_container" type="id"/>
    <declare-styleable name="DialogFragmentNavigator">
        <attr name="android:name"/>
    </declare-styleable>
    <declare-styleable name="FragmentNavigator">
        <attr name="android:name"/>
    </declare-styleable>
    <declare-styleable name="NavHostFragment">
        <attr format="boolean" name="defaultNavHost"/>
    </declare-styleable>
</resources>