<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_3dp"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/tag_select"/>

        <Spinner
            android:id="@+id/spinner_manufactorer"
            android:background="@drawable/spinner_background"
            android:minHeight="@dimen/min_height_40dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:entries="@array/temp_manufacturer"
             />

    </LinearLayout>

    <include layout="@layout/temperature_tag_item"/>

    <ListView
        android:id="@+id/listVew_epc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_3dp"
        android:orientation="horizontal">
        <Button
            android:id="@+id/button_read"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/min_height_60dp"
            android:layout_margin="3dp"
            android:textSize="@dimen/text_size_18sp"
            android:layout_weight="1"
            android:text="@string/read"
            />

        <Button
            android:id="@+id/button_clean"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/min_height_60dp"
            android:layout_margin="3dp"
            android:textSize="@dimen/text_size_18sp"
            android:text="@string/clean"
            />
    </LinearLayout>

</LinearLayout>