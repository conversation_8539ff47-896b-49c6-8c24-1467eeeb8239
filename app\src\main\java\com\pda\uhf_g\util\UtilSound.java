package com.pda.uhf_g.util;

import android.content.Context;
import android.media.AudioManager;
import android.media.SoundPool;
import android.util.Log;

import com.pda.uhf_g.R;

import java.util.HashMap;

public class UtilSound {
    private static final String TAG = "UtilSound";
    
    private SoundPool soundPool;
    private HashMap<Integer, Integer> soundMap;
    private Context context;
    private boolean isLoaded = false;

    public UtilSound(Context context) {
        this.context = context;
        initSoundPool();
    }

    private void initSoundPool() {
        soundPool = new SoundPool.Builder()
                .setMaxStreams(5)
                .build();
        
        soundMap = new HashMap<>();
        
        soundPool.setOnLoadCompleteListener(new SoundPool.OnLoadCompleteListener() {
            @Override
            public void onLoadComplete(SoundPool soundPool, int sampleId, int status) {
                if (status == 0) {
                    isLoaded = true;
                    Log.d(TAG, "Sound loaded successfully");
                } else {
                    Log.e(TAG, "Failed to load sound");
                }
            }
        });
        
        loadSounds();
    }

    private void loadSounds() {
        try {
            // Load beep sound for successful tag read
            int beepSoundId = soundPool.load(context, R.raw.beep, 1);
            soundMap.put(R.raw.beep, beepSoundId);
            
            // Load error sound for failed operations
            int errorSoundId = soundPool.load(context, R.raw.error, 1);
            soundMap.put(R.raw.error, errorSoundId);
            
        } catch (Exception e) {
            Log.e(TAG, "Error loading sounds: " + e.getMessage());
        }
    }

    public void playBeep() {
        playSound(R.raw.beep);
    }

    public void playError() {
        playSound(R.raw.error);
    }

    private void playSound(int soundResource) {
        if (soundPool != null && isLoaded && soundMap.containsKey(soundResource)) {
            try {
                AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
                float volume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
                float maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
                float volumeRatio = volume / maxVolume;
                
                int soundId = soundMap.get(soundResource);
                soundPool.play(soundId, volumeRatio, volumeRatio, 1, 0, 1.0f);
                
            } catch (Exception e) {
                Log.e(TAG, "Error playing sound: " + e.getMessage());
            }
        } else {
            Log.w(TAG, "Sound not ready or not found");
        }
    }

    public void release() {
        if (soundPool != null) {
            soundPool.release();
            soundPool = null;
        }
        if (soundMap != null) {
            soundMap.clear();
        }
        isLoaded = false;
    }

    public boolean isReady() {
        return isLoaded && soundPool != null;
    }
}
