<component name="libraryTable">
  <library name="Gradle: com.android.support:support-core-ui:26.1.0@aar">
    <CLASSES>
      <root url="jar://G:/AndroidCache/.gradle/caches/transforms-2/files-2.1/54addbd48bc777bd7c2feb0c98546e12/support-core-ui-26.1.0/jars/classes.jar!/" />
      <root url="file://G:/AndroidCache/.gradle/caches/transforms-2/files-2.1/54addbd48bc777bd7c2feb0c98546e12/support-core-ui-26.1.0/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://G:/AndroidCache/.gradle/caches/modules-2/files-2.1/com.android.support/support-core-ui/26.1.0/4d52cef4a24bf9939de1d256ca9f0974c6e636a4/support-core-ui-26.1.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>