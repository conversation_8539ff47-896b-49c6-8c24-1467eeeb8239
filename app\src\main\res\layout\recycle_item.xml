<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">
    <TextView
        android:id="@+id/index"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="2" />

    <TextView
        android:id="@+id/type"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="2" />

    <TextView
        android:id="@+id/ctesius_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:visibility="gone"
        android:text="-"
        android:layout_weight="1"
        />


    <TextView
        android:id="@+id/moisture_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="-"
        android:layout_weight="1"
        android:visibility="gone" />

    <TextView
        android:id="@+id/epcBank_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="-"
        android:visibility="gone" />

    <TextView
        android:id="@+id/ltu27_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="-"
        android:visibility="gone" />

    <TextView
        android:id="@+id/ltu31_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:text="-"
        android:visibility="gone" />


    <TextView
        android:id="@+id/sensor_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:visibility="gone"
        android:text="-"
        />

    <TextView
        android:id="@+id/epc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="10" />

    <TextView
        android:id="@+id/tid"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:visibility="gone"
        android:text="-"
        />

    <TextView
        android:id="@+id/userData"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:visibility="gone"
        android:text="-" />

    <TextView
        android:id="@+id/reserveData"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:text="-"
        android:visibility="gone" />

    <TextView
        android:id="@+id/count"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="2" />

    <TextView
        android:id="@+id/rssi"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="1" />

</LinearLayout>

    <LinearLayout
        android:id="@+id/layout_tid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:orientation="horizontal">


        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Tid:"
            android:layout_weight="2" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:visibility="gone"
            android:text="-"
            android:layout_weight="1"
            />


        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="-"
            android:layout_weight="1"
            android:visibility="gone" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="-"
            android:visibility="gone" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="-"
            android:visibility="gone" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="-"
            android:visibility="gone" />


        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:visibility="gone"
            android:text="-"
            />

        <TextView
            android:id="@+id/tv_tid"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:background="@color/teal_200"
            android:layout_weight="10" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:visibility="gone"
            android:text="-"
            />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:visibility="gone"
            android:text="-" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="-"
            android:visibility="gone" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_weight="1" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_weight="1" />

    </LinearLayout>

</LinearLayout>