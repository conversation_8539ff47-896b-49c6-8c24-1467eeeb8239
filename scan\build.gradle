apply plugin: 'com.android.application'

android {
    namespace 'com.hdhe.scantest'
    compileSdk 34

    defaultConfig {
        applicationId "com.hdhe.scantest"
        minSdkVersion 21
        targetSdk 34
        versionCode 5
        versionName "1.5"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        buildConfig true
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            def fileName = "HwScanTest_v${defaultConfig.versionName}.apk"
            outputFileName = fileName
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
