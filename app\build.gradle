plugins {
    id 'com.android.application'
}

android {
    namespace 'com.pda.uhf_g'
    compileSdk 30
    buildToolsVersion "30.0.3"

    buildFeatures {
        buildConfig true
    }
    defaultConfig {
        applicationId "com.pda.uhf_g"
        minSdk 19
        targetSdk 30
        versionCode 36
        versionName "3.6"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            def fileName = "uhfg_v${defaultConfig.versionName}.apk"
            outputFileName = fileName
        }
    }
    buildTypes {
        debug {
            buildConfigField("String", "BUILD_TIME", getDate())
        }
        release {
            buildConfigField("String", "BUILD_TIME", getDate())
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

static String getDate() {
    Date date = new Date()
    return "\"" + date.format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("GMT+08:00")) + "\""
}

dependencies {

    implementation 'com.android.support:appcompat-v7:28.0.0'
    //noinspection GradleCompatible
    implementation 'com.android.support:design:28.0.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'com.android.support.constraint:constraint-layout:1.1.3'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation 'androidx.navigation:navigation-fragment:2.2.2'
    implementation 'androidx.navigation:navigation-ui:2.2.2'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation files('libs\\reader(1).jar')
    implementation files('libs\\ModuleAPI_J.jar')
    implementation files('libs\\App_Demo_API.jar')
    implementation files('libs\\DeviceAPIver20150204.jar')
    implementation files('libs\\logutil-*******.jar')
    implementation files('libs\\UHF67_v3.6.jar')
    //implementation files('libs\\UHF67_v1.0.15.jar')
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    // implementation 'me.weyye.hipermission:library:1.0.7' // 库不可用，暂时注释
    implementation 'net.sourceforge.jexcelapi:jxl:2.6.12'
    implementation 'com.jakewharton:butterknife:10.1.0'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.1.0'

}