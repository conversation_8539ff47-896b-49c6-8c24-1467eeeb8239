<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.fragment.InventoryFragment">




            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/tabHead"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    >

                    <include layout="@layout/inventory_item" />
                </LinearLayout>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@android:color/darker_gray"/>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <ListView
                        android:id="@+id/listview_epc"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycle"
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                    </androidx.recyclerview.widget.RecyclerView>
                </LinearLayout>

            </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="center"
                android:layout_weight="3">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <CheckBox
                        android:id="@+id/checkbox_multi_tag"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/multi_tag"/>
                    <CheckBox
                        android:id="@+id/checkbox_tid"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tid"/>
                    <CheckBox
                        android:id="@+id/checkbox_loop"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:checked="true"
                        android:visibility="gone"
                        android:layout_height="wrap_content"
                        android:text="@string/loop"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_gravity="center"
                android:layout_weight="2">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/all_tags"
                             />

                        <TextView
                            android:id="@+id/textView_all_tags"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                             />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/speed"
                             />

                        <TextView
                            android:id="@+id/textView_speed"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                             />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/tag_count"
                         />

                    <TextView
                        android:id="@+id/textView_readCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                         />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/time"
                         />

                    <TextView
                        android:id="@+id/textView_timeCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="00:00:00(s)"
                         />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <Button
                android:id="@+id/button_inventory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:minHeight="@dimen/min_height_60dp"
                android:layout_margin="3dp"
                android:textSize="@dimen/text_size_18sp"
                android:text="@string/start_inventory"/>
            <Button
                android:id="@+id/button_cus_read"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/min_height_60dp"
                android:layout_margin="3dp"
                android:textSize="@dimen/text_size_18sp"
                android:visibility="gone"
                android:text="@string/cus_read"/>

            <Button
                android:id="@+id/button_excel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/min_height_60dp"
                android:layout_margin="3dp"
                android:textSize="@dimen/text_size_18sp"
                android:text="@string/excel_export"/>

            <Button
                android:id="@+id/button_clean"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/min_height_60dp"
                android:layout_margin="3dp"
                android:textSize="@dimen/text_size_18sp"
                android:text="@string/clean"/>
        </LinearLayout>


    </LinearLayout>




</LinearLayout>