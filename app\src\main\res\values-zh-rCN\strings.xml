<resources>
    <string name="app_name">UHF-G</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="nav_header_title">Android Studio</string>
    <string name="nav_header_subtitle"><EMAIL></string>
    <string name="nav_header_desc">Navigation header</string>
    <string name="action_settings">Settings</string>

    <string name="menu_home">Home</string>
    <string name="menu_gallery">Gallery</string>
    <string name="menu_slideshow">Slideshow</string>
    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>


    <string name="invenroty">盘存</string>
    <string name="frequency_settings">跳频管理</string>
    <string name="settings">设置</string>

    <string name="sn">SN</string>
    <string name="tag_type">类型</string>
    <string name="epc">EPC</string>
    <string name="password">PASSWORD</string>
    <string name="user">USER</string>
    <string name="tag_count">次数</string>
    <string name="rssi">rssi</string>
    <string name="multi_tag">多标签</string>
    <string name="tid">TID</string>
    <string name="loop">连续读取</string>
    <string name="type_6c">6C</string>
    <string name="type_6b">6B</string>
    <string name="type_gb">GB</string>
    <string name="type_gjb">GJB</string>
    <string name="all_tags">张数:</string>
    <string name="speed">速度:</string>
    <string name="time">时间:</string>
    <string name="start_inventory">开始盘存</string>
    <string name="stop_inventory">停止盘存</string>
    <string name="cus_read">Cus读</string>
    <string name="excel_export">excel导出</string>
    <string name="clean">清屏</string>
    <string name="exit_app">再按一次返回键，退出程序</string>
    <string name="module_init_success">模块初始化成功</string>
    <string name="module_init_fail">模块初始失败</string>

    <string name="communication_timeout">通讯超时</string>
    <string name="unknown">未知错误</string>
    <string name="read_write_tag">读写标签</string>
    <string name="modify_epc">修改EPC</string>
    <string name="tag_select">标签选择:</string>
    <string name="start_addr">起始地址(word):</string>
    <string name="length">长度(word):</string>
    <string name="access_password">访问密码(hex):</string>
    <string name="data">写数据(hex):</string>
    <string name="read_data">读数据(hex):</string>
    <string name="read">读标签</string>
    <string name="write">写标签</string>
    <string name="new_epc">新EPC(hex):</string>
    <string name="please_input_hex_string">输入双字节长度的EPC</string>

    <string name="lock_tag">锁定标签</string>
    <string name="lock_data">锁定区:</string>
    <string name="lock_type">锁定类型:</string>

    <string name="lock_tips">提示:锁定标签前请先修改密码并锁定密码区，注意永久锁定标签后无法解锁，且无法写入数据</string>
    <string name="kill_tag">销毁标签</string>
    <string name="kill_password">销毁密码(hex):</string>
    <string name="please_inventory">请先盘存标签后再进行读写操作</string>

    <string name="start_address_not_null">起始地址不能为空</string>
    <string name="len_not_null">长度不能为空</string>
    <string name="access_password_not_null">访问密码不能为空</string>
    <string name="read_fail">读数据失败</string>
    <string name="write_fail">写数据失败</string>
    <string name="write_success">写数据成功</string>
    <string name="filter">过滤</string>
    <string name="please_input_right_access_password">请输入4字节长度的十六进制密码</string>
    <string name="please_input_right_write_data">请输入双字节位数长度的十六进制数据</string>
    <string name="please_input_right_epc">请输入4的整数倍长度的十六进制新EPC</string>

    <string name="modify_success">新EPC号修改成功，请重新盘存测试</string>
    <string name="modify_fail">修改写入失败</string>
    <string name="lock_success">锁定成功</string>
    <string name="lock_fail">锁定失败</string>

    <string name="kill_success">销毁成功</string>
    <string name="kill_fail">销毁失败</string>

    <string name="base_settings">基本参数设置</string>
    <string name="work_freq">工作频段:</string>
    <string name="power">输出功率:</string>
    <string name="temperature">温度:</string>
    <string name="temp">温度</string>
    <string name="inventory_settings">查询参数设置</string>

    <string name="select_epc">单个点亮epc号:</string>
    <string name="led_inventory">Led标签盘点</string>

    <string name="query">查询</string>
    <string name="q_value">Q值:</string>
    <string name="inventory_type">盘存方式:</string>
    <string name="fastid">FastID</string>

    <string name="query_success">查询成功</string>
    <string name="query_fail">查询失败</string>
    <string name="set_success">设置成功</string>
    <string name="set_fail">设置失败</string>
    <string name="query_region_fail">查询功率失败</string>
    <string name="help">帮助</string>
    <string name="store">存储</string>
    <string name="export_excel_need_permission">导出excel所需权限</string>
    <string name="firmware">固件版本:%s</string>
    <string name="soft_version">软件版本号:%s</string>
    <string name="version_date">修改日期:%s</string>
    <string name="about">关于</string>

    <string name="manufacturer">标签厂家:</string>
    <string name="temp_tag">温度标签</string>
    <string name="stop_read">停止读取</string>

    <string name="timeInterval">间隔时间:</string>
    <string name="dwelltime">驻留时间:</string>
    <string name="strrb1">性能优先</string>
    <string name="strrb2">平衡配置</string>
    <string name="strrb3">续航优先</string>
    <string name="strrb4">自定义</string>
    <string name="getstr">读取</string>
    <string name="setstr">设置</string>

    <string-array name="temp_manufacturer">
        <item>悦和标签</item>
        <item>宜链标签</item>
    </string-array>

    <string-array name="work_freq">
        <item>中国1_920_925</item>
        <item>北美_902_928</item>
        <item>欧洲_865_867</item>
        <item>全频段_840_960</item>
    </string-array>


    <!--锁定类型-->
    <string-array name="lock_data">
        <item>访问密码</item>
        <item>销毁密码</item>
        <item>EPC区</item>
        <item>TID区</item>
        <item>USER区</item>
    </string-array>

    <!--锁定类型-->
    <string-array name="lock_type">
        <item>开放</item>
        <item>锁定</item>
        <item>永久锁定</item>
    </string-array>

    <!--session-->
    <string-array name="session_arrays">
        <item>S0</item>
        <item>S1</item>
        <item>S2-多标签</item>
        <item>S3</item>
    </string-array>

    <!--盘存方式AB面-->
    <string-array name="inventory_type_arrays">
        <item>A面</item>
        <item>B面</item>
        <item>A|B面</item>
    </string-array>

    <!--Q值范围-->
    <string-array name="q_value_arrays">
        <item>0-单标签</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4-多标签</item>
        <item>5</item>
        <item>6</item>
        <item>7</item>
        <item>8</item>
        <item>9</item>
        <item>10</item>
        <item>11</item>
        <item>12</item>
        <item>13</item>
        <item>14</item>
        <item>15</item>

    </string-array>

    <!--功率范围-->
    <string-array name="power_arrays">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
        <item>7</item>
        <item>8</item>
        <item>9</item>
        <item>10</item>
        <item>11</item>
        <item>12</item>
        <item>13</item>
        <item>14</item>
        <item>15</item>
        <item>16</item>
        <item>17</item>
        <item>18</item>
        <item>19</item>
        <item>20</item>
        <item>21</item>
        <item>22</item>
        <item>23</item>
        <item>24</item>
        <item>25</item>
        <item>26</item>
        <item>27</item>
        <item>28</item>
        <item>29</item>
        <item>30</item>
        <item>31</item>
        <item>32</item>
        <item>33</item>
    </string-array>


    <!--国标标签操作区域-->
    <string-array name="gb_membank">
        <item>标签编码区</item>
        <item>标签安全区</item>
        <item>User0</item>
        <item>User1</item>
        <item>User2</item>
        <item>User3</item>
        <item>User4</item>
        <item>User5</item>
        <item>User6</item>
        <item>User7</item>
        <item>User8</item>
        <item>User9</item>
        <item>User10</item>
        <item>User11</item>
        <item>User12</item>
        <item>User13</item>
        <item>User14</item>
        <item>User15</item>
    </string-array>

    <!--国标标签锁定类型-->
    <string-array name="gb_lock_type" >
        <item>可读可写</item>
        <item>可读不可写</item>
        <item>不可读可写</item>
        <item>不可读不可写</item>
        <item>安全模式1</item>
        <item>安全模式2</item>
        <item>安全模式3</item>
    </string-array>


    <string name="help_tips">
          以下为6C超高频标签的说明，超高频标签实际就是一个小的存储空间，模块只是通过特殊命令来读取标签中的数据，所以可以读写多少长度数据，由标签本身决定，具体可询问标签供应商
\n注意：以下所有的读写操作都是以十六进制形式进行的，数据长度必须以字(word)为单位（2byte，即4位，如0001）

标签存储器分为哪几个区？\n
分为Reserved（保留区，存储销毁密码和访问密码），\n
EPC（电子产品代码），\n
TID（标签识别号）和
User（用户）四个独立的存储区块（Bank）。\n

1. RESERVER区：8byte(4字)大小，前4byte(2字)为摧毁密码（用于摧毁标签，一般用不到），后4byte(2字)为访问密码（用于进行写数据和锁定操作），默认值为：0000 0000 (摧毁密码) 0000 0000 (访问密码)\n

2. TID区：12byte(6字)大小，只可读，不可写，出厂已经写入，为标签的唯一标识符，电子标签的产品类识别号，每个生产厂商的TID号都会不同。\n

3. EPC区的读写\n
	EPC为识别标签对象的电子产品码，EPC与TID相比，区别在于EPC可以用户手动写入修改\n

	EPC区的数据结构是，第一个word是校验位，第二个word是长度控制，之后的才是数据存储位置，盘存的时候，是根据长度控制位所表示的长度来显示EPC的。\n
	实际写入EPC数据的时候，起始地址填1，即从长度控制位开始写入数据，校验位会自动计算不用手动写入。\n
	长度控制位计算： EPC数据长度 * 2 = i;  将i转换为十六进制表示，然后在转换后的结果后面加00，最终得到的就是长度控制位的数据.\n
				如：0001，  i = 4 * 2 = 8  --->  08 --->0800，长度控制位为0800\n
				程序中计算：    byte[] newEPCByte = Tools.HexString2Bytes(newEPC);\n
								byte[] pcByte = new byte[] { 0x00, 0x00 };\n
								pcByte[0] = (byte) (newEPCByte.length * 4);\n
								String pc = Tools.Bytes2HexString(pcByte, 2));\n
	长度根据实际需要写入的数据长度填，比如：0001，长度为1word，4位(需要注意的是，数据位长度必须为4的倍数)，加上长度控制位0800，软件中长度填写2；\n

	例： 0001\n
		起始地址：1，长度：2，数据：08000001\n

4. USER区：该存贮器的长度由各个电子标签的生产厂商确定，USER区可以写入用户自定义的数据（以16进制形式）\n

5. 关于标签锁定\n
	锁定状态分为四种，分两种情况分别进行说明：\n
	1）如果没有修改访问密码，为默认的00000000情况：\n
		未锁定：使用密码00000000可读、可写\n
		暂时锁定：使用密码00000000可读、可写\n
		永久锁定：使用密码00000000可读，不可写\n
		解锁定：将暂时锁定的区域解锁，变成未锁定状态；永久锁定的区域无法被解锁\n
	2）如果修改了访问密码，不为默认的00000000情况，假设修改为000000FF\n
		未锁定：使用密码00000000或者000000FF可读、可写\n
		暂时锁定：RESERVER区暂时锁定后，使用密码000000FF可读、可写；其他区域（TID除外）暂时锁定后，使用密码00000000或000000FF可读，使用密码000000FF可写\n
		永久锁定：RESERVER区永久锁定后，使用密码000000FF可读，不可写；其他区域（TID除外）永久锁定后使用密码00000000或000000FF可读，不可写\n
		解锁定：将暂时锁定的区域解锁，变成未锁定状态；永久锁定的区域无法被解锁\n
    </string>
</resources>