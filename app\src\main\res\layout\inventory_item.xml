<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">


    <TextView
        android:id="@+id/textView_sn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="2"
        android:text="@string/sn" />

    <TextView
        android:id="@+id/textView_type"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="2"
        android:text="@string/tag_type" />

    <TextView
        android:id="@+id/textView_epc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="10"
        android:text="@string/epc" />


    <TextView
        android:id="@+id/textView_count"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="2"
        android:text="@string/tag_count" />

    <TextView
        android:id="@+id/textView_rssi"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_weight="1"
        android:text="@string/rssi" />
</LinearLayout>