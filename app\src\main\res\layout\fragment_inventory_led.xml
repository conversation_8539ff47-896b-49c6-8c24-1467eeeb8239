<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.fragment.InventoryLedFragment">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/tabHead"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <include layout="@layout/inventory_item" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@android:color/darker_gray" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <ListView
                android:id="@+id/listview_epc"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

            </androidx.recyclerview.widget.RecyclerView>
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="2"
                android:orientation="vertical"
                android:layout_margin="10dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/select_epc"
                        android:textSize="@dimen/text_size_20sp"
                        />

                    <TextView
                        android:id="@+id/textView_led"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textSize="@dimen/text_size_20sp"
                        />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/button_inventory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="3dp"
                android:layout_weight="1"
                android:minHeight="@dimen/min_height_60dp"
                android:text="@string/start_inventory"
                android:textSize="@dimen/text_size_18sp" />

            <Button
                android:id="@+id/button_cus_read"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="3dp"
                android:minHeight="@dimen/min_height_60dp"
                android:text="@string/cus_read"
                android:textSize="@dimen/text_size_18sp"
                android:visibility="gone" />


            <Button
                android:id="@+id/button_clean"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="3dp"
                android:minHeight="@dimen/min_height_60dp"
                android:text="@string/clean"
                android:textSize="@dimen/text_size_18sp" />
        </LinearLayout>


    </LinearLayout>


</LinearLayout>